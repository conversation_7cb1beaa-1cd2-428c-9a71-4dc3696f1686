@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 0%; /* #000000 */
  --foreground: 0 0% 100%; /* #FFFFFF */
  --muted: 0 0% 10%; /* #1A1A1A */
  --muted-foreground: 0 0% 60%; /* #999999 */
  --popover: 0 0% 5%; /* #0D0D0D */
  --popover-foreground: 0 0% 100%; /* #FFFFFF */
  --card: 0 0% 5%; /* #0D0D0D */
  --card-foreground: 0 0% 100%; /* #FFFFFF */
  --border: 0 0% 20%; /* #333333 */
  --input: 0 0% 15%; /* #262626 */
  --primary: 0 0% 100%; /* #FFFFFF */
  --primary-foreground: 0 0% 0%; /* #000000 */
  --secondary: 0 0% 10%; /* #1A1A1A */
  --secondary-foreground: 0 0% 100%; /* #FFFFFF */
  --accent: 0 0% 15%; /* #262626 */
  --accent-foreground: 0 0% 100%; /* #FFFFFF */
  --destructive: 0 84% 60%; /* #EF4444 */
  --destructive-foreground: 0 0% 100%; /* #FFFFFF */
  --ring: 0 0% 100%; /* #FFFFFF */
  --radius: 0.75rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .glass-morphism {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .comet {
    position: absolute;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 40px rgba(255, 255, 255, 0.4);
  }

  .comet::before {
    content: '';
    position: absolute;
    top: 50%;
    right: 100%;
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8));
    transform: translateY(-50%);
  }

  .comet-particle {
    position: absolute;
    width: 3px;
    height: 3px;
    background: white;
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.8), 0 0 20px rgba(255, 255, 255, 0.4);
  }

  .bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }

  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
  }

  .horizontal-scroll {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .horizontal-scroll::-webkit-scrollbar {
    display: none;
  }

  .text-gradient {
    background: linear-gradient(135deg, #ffffff 0%, #cccccc 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .project-card {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .project-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 25px 50px rgba(255, 255, 255, 0.1);
  }

  .nav-indicator {
    transition: all 0.3s ease;
    transform: scaleX(0);
    transform-origin: left;
  }

  .nav-indicator.active {
    transform: scaleX(1);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes comet {
  0% { transform: translateX(-100vw) translateY(0); opacity: 0; }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { transform: translateX(100vw) translateY(0); opacity: 0; }
}

@keyframes glow {
  0%, 100% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.3); }
  50% { text-shadow: 0 0 40px rgba(255, 255, 255, 0.6), 0 0 60px rgba(255, 255, 255, 0.3); }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-comet {
  animation: comet 4s linear infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}
